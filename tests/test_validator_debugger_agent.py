"""
Unit tests for ValidatorDebuggerAgent module.

This module contains comprehensive tests for the ValidatorDebuggerAgent class,
including error classification, diagnosis, fix generation, and inner loop integration.

Author: Augment Agent
Date: 2025-07-18
"""

import os
import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from dataclasses import dataclass

# Add the project root to the path
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from agents.validator_debugger_agent import (
    ValidatorDebuggerAgent,
    ValidatorDebuggerConfig,
    ErrorType,
    DiagnosisConfidence,
    ErrorDiagnosis,
    ValidationResult,
    ValidatorDebuggerError
)

# Mock classes for testing
@dataclass
class MockBlenderOutput:
    """Mock BlenderOutput for testing."""
    status: str
    stdout: str
    stderr: str
    return_code: int
    execution_time: float
    output_files: list
    error_details: dict = None

class MockBlenderExecutionStatus:
    """Mock BlenderExecutionStatus for testing."""
    SUCCESS = "success"
    SCRIPT_ERROR = "script_error"
    TIMEOUT = "timeout"
    ERROR = "error"

class MockRetrievalResult:
    """Mock RetrievalResult for testing."""
    def __init__(self, content: str):
        self.chunk = Mock()
        self.chunk.content = content

class MockKnowledgeAgent:
    """Mock KnowledgeAgent for testing."""
    def query_knowledge(self, query: str, top_k: int = 5):
        return [
            MockRetrievalResult("bpy.ops.mesh.primitive_cube_add() creates a cube mesh"),
            MockRetrievalResult("bpy.context.object refers to the active object"),
            MockRetrievalResult("bpy.data.objects contains all scene objects")
        ]


class TestValidatorDebuggerAgent:
    """Test suite for ValidatorDebuggerAgent class."""
    
    @pytest.fixture
    def config(self):
        """Fixture providing test configuration."""
        return ValidatorDebuggerConfig(
            openai_model="gpt-4",
            max_retries=2,
            use_knowledge_agent=True,
            enable_auto_fix=True,
            confidence_threshold=0.6,
            max_fix_attempts=3
        )
    
    @pytest.fixture
    def mock_knowledge_agent(self):
        """Fixture providing mock knowledge agent."""
        return MockKnowledgeAgent()
    
    @pytest.fixture
    def agent(self, config, mock_knowledge_agent):
        """Fixture providing ValidatorDebuggerAgent instance."""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            return ValidatorDebuggerAgent(
                config=config,
                knowledge_agent=mock_knowledge_agent,
                openai_api_key="test-key"
            )
    
    @pytest.fixture
    def agent_no_openai(self, config, mock_knowledge_agent):
        """Fixture providing ValidatorDebuggerAgent without OpenAI."""
        return ValidatorDebuggerAgent(
            config=config,
            knowledge_agent=mock_knowledge_agent,
            openai_api_key=None
        )
    
    # Test data fixtures
    @pytest.fixture
    def syntax_error_output(self):
        """Mock Blender output with syntax error."""
        return MockBlenderOutput(
            status=MockBlenderExecutionStatus.SCRIPT_ERROR,
            stdout="Blender started",
            stderr="""
  File "/tmp/script.py", line 3
    invalid syntax here
                      ^
SyntaxError: invalid syntax
""",
            return_code=1,
            execution_time=0.5,
            output_files=[]
        )
    
    @pytest.fixture
    def name_error_output(self):
        """Mock Blender output with name error."""
        return MockBlenderOutput(
            status=MockBlenderExecutionStatus.SCRIPT_ERROR,
            stdout="Blender started",
            stderr="""
Traceback (most recent call last):
  File "/tmp/script.py", line 4, in <module>
    undefined_variable.some_method()
NameError: name 'undefined_variable' is not defined
""",
            return_code=1,
            execution_time=0.8,
            output_files=[]
        )
    
    @pytest.fixture
    def bpy_api_error_output(self):
        """Mock Blender output with bpy API error."""
        return MockBlenderOutput(
            status=MockBlenderExecutionStatus.SCRIPT_ERROR,
            stdout="Blender started",
            stderr="""
Traceback (most recent call last):
  File "/tmp/script.py", line 6, in <module>
    bpy.ops.mesh.primitive_cube_add(invalid_parameter="test")
RuntimeError: Operator bpy.ops.mesh.primitive_cube_add poll() failed, context is incorrect
""",
            return_code=1,
            execution_time=1.2,
            output_files=[]
        )
    
    @pytest.fixture
    def success_output(self):
        """Mock successful Blender output."""
        return MockBlenderOutput(
            status=MockBlenderExecutionStatus.SUCCESS,
            stdout="Successfully created cube and saved to /tmp/test.blend",
            stderr="",
            return_code=0,
            execution_time=2.1,
            output_files=["/tmp/test.blend"]
        )
    
    @pytest.fixture
    def sample_code(self):
        """Sample Blender Python code."""
        return """
import bpy

# Clear existing objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create a cube
bpy.ops.mesh.primitive_cube_add(size=2.0, location=(0, 0, 0))

# Save the file
bpy.ops.wm.save_as_mainfile(filepath="/tmp/test.blend")
"""
    
    def test_init_with_config(self, config, mock_knowledge_agent):
        """Test ValidatorDebuggerAgent initialization with config."""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            agent = ValidatorDebuggerAgent(
                config=config,
                knowledge_agent=mock_knowledge_agent,
                openai_api_key="test-key"
            )
            assert agent.config == config
            assert agent.knowledge_agent == mock_knowledge_agent
            assert agent.openai_client is not None
    
    def test_init_without_openai_key(self, config, mock_knowledge_agent):
        """Test initialization without OpenAI API key."""
        agent = ValidatorDebuggerAgent(
            config=config,
            knowledge_agent=mock_knowledge_agent,
            openai_api_key=None
        )
        assert agent.openai_client is None
    
    def test_error_pattern_initialization(self, agent):
        """Test error pattern initialization."""
        patterns = agent.error_patterns
        assert ErrorType.SYNTAX_ERROR in patterns
        assert ErrorType.NAME_ERROR in patterns
        assert ErrorType.BPY_API_ERROR in patterns
        assert len(patterns[ErrorType.SYNTAX_ERROR]) > 0
    
    def test_classify_error_syntax_error(self, agent):
        """Test error classification for syntax errors."""
        error_text = "SyntaxError: invalid syntax"
        error_type = agent._classify_error(error_text)
        assert error_type == ErrorType.SYNTAX_ERROR
    
    def test_classify_error_name_error(self, agent):
        """Test error classification for name errors."""
        error_text = "NameError: name 'undefined_variable' is not defined"
        error_type = agent._classify_error(error_text)
        assert error_type == ErrorType.NAME_ERROR
    
    def test_classify_error_bpy_api_error(self, agent):
        """Test error classification for bpy API errors."""
        error_text = "RuntimeError: Operator bpy.ops.mesh.primitive_cube_add poll() failed"
        error_type = agent._classify_error(error_text)
        assert error_type == ErrorType.BPY_API_ERROR
    
    def test_classify_error_unknown(self, agent):
        """Test error classification for unknown errors."""
        error_text = "Some completely unknown error message"
        error_type = agent._classify_error(error_text)
        assert error_type == ErrorType.UNKNOWN_ERROR
    
    def test_extract_line_numbers(self, agent):
        """Test extraction of line numbers from traceback."""
        error_text = '''
Traceback (most recent call last):
  File "/tmp/script.py", line 4, in <module>
    undefined_variable.some_method()
  File "/tmp/other.py", line 10, in function
    another_error()
NameError: name 'undefined_variable' is not defined
'''
        line_numbers = agent._extract_line_numbers(error_text)
        assert 4 in line_numbers
        assert 10 in line_numbers
        assert len(line_numbers) == 2
    
    def test_get_error_knowledge_context(self, agent):
        """Test getting knowledge context for errors."""
        context = agent._get_error_knowledge_context(ErrorType.BPY_API_ERROR, "bpy.ops.mesh error")
        assert len(context) > 0
        assert any("cube" in ctx.lower() for ctx in context)
    
    def test_validate_and_debug_success(self, agent, success_output, sample_code):
        """Test validation of successful execution."""
        result = agent.validate_and_debug(success_output, sample_code)
        assert result.is_valid is True
        assert result.diagnosis is None
        assert result.fixed_code is None
        assert result.fix_applied is False
    
    def test_validate_and_debug_syntax_error(self, agent_no_openai, syntax_error_output, sample_code):
        """Test validation with syntax error (no OpenAI)."""
        result = agent_no_openai.validate_and_debug(syntax_error_output, sample_code)
        assert result.is_valid is False
        assert result.diagnosis is not None
        assert result.diagnosis.error_type == ErrorType.SYNTAX_ERROR
        assert result.diagnosis.confidence == DiagnosisConfidence.MEDIUM
    
    def test_validate_and_debug_name_error(self, agent_no_openai, name_error_output, sample_code):
        """Test validation with name error (no OpenAI)."""
        result = agent_no_openai.validate_and_debug(name_error_output, sample_code)
        assert result.is_valid is False
        assert result.diagnosis is not None
        assert result.diagnosis.error_type == ErrorType.NAME_ERROR
        assert len(result.diagnosis.affected_lines) > 0
    
    def test_pattern_based_diagnosis(self, agent):
        """Test pattern-based diagnosis fallback."""
        diagnosis = agent._pattern_based_diagnosis(
            ErrorType.SYNTAX_ERROR,
            "SyntaxError: invalid syntax",
            [3],
            ["Some context"]
        )
        assert diagnosis.error_type == ErrorType.SYNTAX_ERROR
        assert diagnosis.confidence == DiagnosisConfidence.MEDIUM
        assert len(diagnosis.suggested_fixes) > 0
    
    def test_validate_code_syntax_valid(self, agent):
        """Test code syntax validation with valid code."""
        valid_code = "import bpy\nprint('Hello')"
        assert agent._validate_code_syntax(valid_code) is True
    
    def test_validate_code_syntax_invalid(self, agent):
        """Test code syntax validation with invalid code."""
        invalid_code = "import bpy\nprint('Hello'"  # Missing closing parenthesis
        assert agent._validate_code_syntax(invalid_code) is False

    @patch('openai.OpenAI')
    def test_llm_diagnose_error_success(self, mock_openai_class, agent):
        """Test LLM error diagnosis with successful response."""
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '''
{
    "confidence": "high",
    "description": "Undefined variable error",
    "root_cause": "Variable 'undefined_variable' is not defined before use",
    "suggested_fixes": [
        "Define the variable before using it",
        "Check for typos in variable name"
    ],
    "metadata": {
        "api_issues": [],
        "common_mistake": "yes",
        "fix_complexity": "simple"
    }
}
'''

        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        agent.openai_client = mock_client

        result = agent._llm_diagnose_error(
            "NameError: name 'undefined_variable' is not defined",
            "undefined_variable.method()",
            ErrorType.NAME_ERROR,
            []
        )

        assert result["confidence"] == DiagnosisConfidence.HIGH
        assert "undefined variable" in result["description"].lower()
        assert len(result["suggested_fixes"]) == 2

    @patch('openai.OpenAI')
    def test_llm_diagnose_error_json_parse_failure(self, mock_openai_class, agent):
        """Test LLM error diagnosis with JSON parsing failure."""
        # Mock OpenAI response with invalid JSON
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "This is not valid JSON response"

        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        agent.openai_client = mock_client

        result = agent._llm_diagnose_error(
            "Some error",
            "some code",
            ErrorType.UNKNOWN_ERROR,
            []
        )

        assert result["confidence"] == DiagnosisConfidence.MEDIUM
        assert "parsing" in result["metadata"]

    @patch('openai.OpenAI')
    def test_generate_code_fix_success(self, mock_openai_class, agent):
        """Test code fix generation with successful response."""
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '''
```python
import bpy

# Clear existing objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create a cube (fixed parameter)
bpy.ops.mesh.primitive_cube_add(size=2.0, location=(0, 0, 0))
```
'''

        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        agent.openai_client = mock_client

        diagnosis = ErrorDiagnosis(
            error_type=ErrorType.BPY_API_ERROR,
            confidence=DiagnosisConfidence.HIGH,
            description="API parameter error",
            root_cause="Invalid parameter used",
            affected_lines=[5],
            suggested_fixes=["Fix parameter name"],
            knowledge_context=[]
        )

        original_code = "bpy.ops.mesh.primitive_cube_add(invalid_param=2.0)"
        fixed_code = agent._generate_code_fix(original_code, diagnosis)

        assert fixed_code is not None
        assert "bpy.ops.mesh.primitive_cube_add" in fixed_code
        assert "invalid_param" not in fixed_code

    def test_create_inner_loop_fix_cycle_success(self, agent):
        """Test inner loop fix cycle with successful resolution."""
        # Mock CodeGenerationAgent
        mock_code_agent = Mock()
        mock_generation_result = Mock()
        mock_generation_result.generated_code = "import bpy\nbpy.ops.mesh.primitive_cube_add()"
        mock_code_agent.generate_blender_code.return_value = mock_generation_result

        # Mock BlenderExecutor
        mock_blender_executor = Mock()
        mock_success_output = MockBlenderOutput(
            status=MockBlenderExecutionStatus.SUCCESS,
            stdout="Success",
            stderr="",
            return_code=0,
            execution_time=1.0,
            output_files=[]
        )
        mock_blender_executor.execute_script.return_value = mock_success_output

        # Test the cycle
        original_spec = {"objects": [{"geometry": {"type": "Cube"}}]}
        result = agent.create_inner_loop_fix_cycle(
            mock_code_agent,
            mock_blender_executor,
            original_spec,
            max_attempts=3
        )

        assert result["success"] is True
        assert result["attempts"] == 1
        assert result["final_code"] is not None
        assert len(result["error_history"]) == 0

    def test_create_inner_loop_fix_cycle_failure(self, agent_no_openai):
        """Test inner loop fix cycle with persistent failure."""
        # Mock CodeGenerationAgent
        mock_code_agent = Mock()
        mock_generation_result = Mock()
        mock_generation_result.generated_code = "import bpy\nundefined_variable.method()"
        mock_code_agent.generate_blender_code.return_value = mock_generation_result

        # Mock BlenderExecutor with persistent error
        mock_blender_executor = Mock()
        mock_error_output = MockBlenderOutput(
            status=MockBlenderExecutionStatus.SCRIPT_ERROR,
            stdout="",
            stderr="NameError: name 'undefined_variable' is not defined",
            return_code=1,
            execution_time=0.5,
            output_files=[]
        )
        mock_blender_executor.execute_script.return_value = mock_error_output

        # Test the cycle
        original_spec = {"objects": [{"geometry": {"type": "Cube"}}]}
        result = agent_no_openai.create_inner_loop_fix_cycle(
            mock_code_agent,
            mock_blender_executor,
            original_spec,
            max_attempts=2
        )

        assert result["success"] is False
        assert result["attempts"] == 2
        assert len(result["error_history"]) == 2

    def test_error_type_enum(self):
        """Test ErrorType enumeration."""
        assert ErrorType.SYNTAX_ERROR.value == "syntax_error"
        assert ErrorType.NAME_ERROR.value == "name_error"
        assert ErrorType.BPY_API_ERROR.value == "bpy_api_error"
        assert len(ErrorType) >= 10  # Should have at least 10 error types

    def test_diagnosis_confidence_enum(self):
        """Test DiagnosisConfidence enumeration."""
        assert DiagnosisConfidence.HIGH.value == "high"
        assert DiagnosisConfidence.MEDIUM.value == "medium"
        assert DiagnosisConfidence.LOW.value == "low"

    def test_error_diagnosis_dataclass(self):
        """Test ErrorDiagnosis dataclass."""
        diagnosis = ErrorDiagnosis(
            error_type=ErrorType.SYNTAX_ERROR,
            confidence=DiagnosisConfidence.HIGH,
            description="Test error",
            root_cause="Test cause",
            affected_lines=[1, 2],
            suggested_fixes=["Fix 1", "Fix 2"],
            knowledge_context=["Context 1"]
        )

        assert diagnosis.error_type == ErrorType.SYNTAX_ERROR
        assert diagnosis.confidence == DiagnosisConfidence.HIGH
        assert len(diagnosis.affected_lines) == 2
        assert len(diagnosis.suggested_fixes) == 2

    def test_validation_result_dataclass(self):
        """Test ValidationResult dataclass."""
        result = ValidationResult(
            is_valid=False,
            diagnosis=None,
            fixed_code="fixed code",
            fix_applied=True,
            validation_time=1.5,
            retry_count=2
        )

        assert result.is_valid is False
        assert result.fixed_code == "fixed code"
        assert result.fix_applied is True
        assert result.validation_time == 1.5

    def test_config_dataclass(self):
        """Test ValidatorDebuggerConfig dataclass."""
        config = ValidatorDebuggerConfig(
            openai_model="gpt-3.5-turbo",
            max_retries=5,
            use_knowledge_agent=False,
            enable_auto_fix=False
        )

        assert config.openai_model == "gpt-3.5-turbo"
        assert config.max_retries == 5
        assert config.use_knowledge_agent is False
        assert config.enable_auto_fix is False


# Integration tests
class TestValidatorDebuggerIntegration:
    """Integration tests for ValidatorDebuggerAgent."""

    @pytest.fixture
    def integration_agent(self):
        """Agent for integration testing."""
        config = ValidatorDebuggerConfig(enable_auto_fix=False)
        return ValidatorDebuggerAgent(config=config, openai_api_key=None)

    def test_end_to_end_error_classification(self, integration_agent):
        """Test end-to-end error classification for all error types."""
        test_cases = [
            ("SyntaxError: invalid syntax", ErrorType.SYNTAX_ERROR),
            ("NameError: name 'x' is not defined", ErrorType.NAME_ERROR),
            ("AttributeError: 'NoneType' object has no attribute 'location'", ErrorType.ATTRIBUTE_ERROR),
            ("TypeError: unsupported operand type(s)", ErrorType.TYPE_ERROR),
            ("ValueError: invalid literal for int()", ErrorType.VALUE_ERROR),
            ("ImportError: No module named 'missing_module'", ErrorType.IMPORT_ERROR),
            ("RuntimeError: Operator bpy.ops.mesh.primitive_cube_add poll() failed", ErrorType.BPY_API_ERROR),
            ("mesh.from_pydata: invalid mesh data", ErrorType.MESH_ERROR),
            ("material.node_tree: Material not found", ErrorType.MATERIAL_ERROR),
            ("addon utils modules: Failed to load addon", ErrorType.ADDON_ERROR)
        ]

        for error_text, expected_type in test_cases:
            classified_type = integration_agent._classify_error(error_text)
            assert classified_type == expected_type, f"Failed to classify: {error_text}"


if __name__ == "__main__":
    pytest.main([__file__])
