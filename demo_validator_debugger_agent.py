#!/usr/bin/env python3
"""
Demo script for ValidatorDebuggerAgent

This script demonstrates the capabilities of the ValidatorDebuggerAgent,
including error classification, diagnosis, and fix generation.

Author: Augment Agent
Date: 2025-07-18
"""

import os
import sys
import json
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from agents.validator_debugger_agent import (
    ValidatorDebuggerAgent,
    ValidatorDebuggerConfig,
    ErrorType,
    DiagnosisConfidence
)
from agents.knowledge_agent import KnowledgeAgent
from blender_interface.blender_executor import BlenderExecutor, BlenderOutput, BlenderExecutionStatus


def create_mock_blender_outputs():
    """Create mock Blender outputs for demonstration."""
    
    # Success case
    success_output = BlenderOutput(
        status=BlenderExecutionStatus.SUCCESS,
        stdout="Successfully created cube and saved to /tmp/demo.blend",
        stderr="",
        return_code=0,
        execution_time=1.5,
        output_files=["/tmp/demo.blend"]
    )
    
    # Syntax error case
    syntax_error_output = BlenderOutput(
        status=BlenderExecutionStatus.SCRIPT_ERROR,
        stdout="Blender started",
        stderr="""
  File "/tmp/script.py", line 5
    bpy.ops.mesh.primitive_cube_add(size=2.0 location=(0, 0, 0))
                                           ^
SyntaxError: invalid syntax
""",
        return_code=1,
        execution_time=0.3,
        output_files=[]
    )
    
    # Name error case
    name_error_output = BlenderOutput(
        status=BlenderExecutionStatus.SCRIPT_ERROR,
        stdout="Blender started",
        stderr="""
Traceback (most recent call last):
  File "/tmp/script.py", line 8, in <module>
    undefined_variable.location = (1, 2, 3)
NameError: name 'undefined_variable' is not defined
""",
        return_code=1,
        execution_time=0.5,
        output_files=[]
    )
    
    # BPY API error case
    bpy_api_error_output = BlenderOutput(
        status=BlenderExecutionStatus.SCRIPT_ERROR,
        stdout="Blender started",
        stderr="""
Traceback (most recent call last):
  File "/tmp/script.py", line 10, in <module>
    bpy.ops.mesh.primitive_cube_add(invalid_parameter="test")
RuntimeError: Operator bpy.ops.mesh.primitive_cube_add poll() failed, context is incorrect
""",
        return_code=1,
        execution_time=0.7,
        output_files=[]
    )
    
    return {
        "success": success_output,
        "syntax_error": syntax_error_output,
        "name_error": name_error_output,
        "bpy_api_error": bpy_api_error_output
    }


def create_sample_codes():
    """Create sample code snippets for demonstration."""
    
    return {
        "success": """
import bpy

# Clear existing objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create a cube
bpy.ops.mesh.primitive_cube_add(size=2.0, location=(0, 0, 0))

# Save the file
bpy.ops.wm.save_as_mainfile(filepath="/tmp/demo.blend")
""",
        
        "syntax_error": """
import bpy

# Clear existing objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create a cube (missing comma - syntax error)
bpy.ops.mesh.primitive_cube_add(size=2.0 location=(0, 0, 0))
""",
        
        "name_error": """
import bpy

# Clear existing objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create a cube
bpy.ops.mesh.primitive_cube_add(size=2.0, location=(0, 0, 0))

# Try to access undefined variable
undefined_variable.location = (1, 2, 3)
""",
        
        "bpy_api_error": """
import bpy

# Clear existing objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create a cube with invalid parameter
bpy.ops.mesh.primitive_cube_add(invalid_parameter="test")
"""
    }


def demo_error_classification():
    """Demonstrate error classification capabilities."""
    print("=" * 60)
    print("DEMO: Error Classification")
    print("=" * 60)
    
    # Initialize agent without OpenAI for basic classification
    config = ValidatorDebuggerConfig(enable_auto_fix=False)
    agent = ValidatorDebuggerAgent(config=config, openai_api_key=None)
    
    # Test error classification
    test_errors = [
        ("SyntaxError: invalid syntax", ErrorType.SYNTAX_ERROR),
        ("NameError: name 'undefined_var' is not defined", ErrorType.NAME_ERROR),
        ("AttributeError: 'NoneType' object has no attribute 'location'", ErrorType.ATTRIBUTE_ERROR),
        ("TypeError: unsupported operand type(s)", ErrorType.TYPE_ERROR),
        ("RuntimeError: Operator bpy.ops.mesh.primitive_cube_add poll() failed", ErrorType.BPY_API_ERROR),
        ("ImportError: No module named 'missing_module'", ErrorType.IMPORT_ERROR),
    ]
    
    for error_text, expected_type in test_errors:
        classified_type = agent._classify_error(error_text)
        status = "✓" if classified_type == expected_type else "✗"
        print(f"{status} {error_text[:50]}... -> {classified_type.value}")
    
    print()


def demo_validation_and_debugging():
    """Demonstrate validation and debugging capabilities."""
    print("=" * 60)
    print("DEMO: Validation and Debugging")
    print("=" * 60)
    
    # Initialize agent
    config = ValidatorDebuggerConfig(enable_auto_fix=False)
    agent = ValidatorDebuggerAgent(config=config, openai_api_key=None)
    
    # Get test data
    outputs = create_mock_blender_outputs()
    codes = create_sample_codes()
    
    # Test different scenarios
    scenarios = ["success", "syntax_error", "name_error", "bpy_api_error"]
    
    for scenario in scenarios:
        print(f"\n--- Testing {scenario.replace('_', ' ').title()} ---")
        
        output = outputs[scenario]
        code = codes[scenario]
        
        # Validate and debug
        result = agent.validate_and_debug(output, code)
        
        print(f"Valid: {result.is_valid}")
        print(f"Validation Time: {result.validation_time:.4f}s")
        
        if result.diagnosis:
            print(f"Error Type: {result.diagnosis.error_type.value}")
            print(f"Confidence: {result.diagnosis.confidence.value}")
            print(f"Description: {result.diagnosis.description}")
            print(f"Root Cause: {result.diagnosis.root_cause}")
            print(f"Affected Lines: {result.diagnosis.affected_lines}")
            print(f"Suggested Fixes: {len(result.diagnosis.suggested_fixes)} fixes")
            for i, fix in enumerate(result.diagnosis.suggested_fixes, 1):
                print(f"  {i}. {fix}")
        
        print()


def demo_inner_loop_simulation():
    """Demonstrate inner loop fix cycle simulation."""
    print("=" * 60)
    print("DEMO: Inner Loop Fix Cycle Simulation")
    print("=" * 60)
    
    # Initialize agent
    config = ValidatorDebuggerConfig(enable_auto_fix=False, max_fix_attempts=3)
    agent = ValidatorDebuggerAgent(config=config, openai_api_key=None)
    
    # Mock CodeGenerationAgent
    class MockCodeGenerationAgent:
        def __init__(self):
            self.attempt = 0
            self.codes = [
                "import bpy\nundefined_variable.method()",  # Will fail
                "import bpy\nbpy.ops.mesh.primitive_cube_add(invalid_param=True)",  # Will fail
                "import bpy\nbpy.ops.mesh.primitive_cube_add(size=2.0)"  # Will succeed
            ]
        
        def generate_blender_code(self, spec, fix_context=None):
            result = type('Result', (), {})()
            result.generated_code = self.codes[min(self.attempt, len(self.codes) - 1)]
            self.attempt += 1
            return result
    
    # Mock BlenderExecutor
    class MockBlenderExecutor:
        def __init__(self):
            self.attempt = 0
        
        def execute_script(self, code):
            self.attempt += 1
            
            if "undefined_variable" in code:
                return BlenderOutput(
                    status=BlenderExecutionStatus.SCRIPT_ERROR,
                    stdout="",
                    stderr="NameError: name 'undefined_variable' is not defined",
                    return_code=1,
                    execution_time=0.5,
                    output_files=[]
                )
            elif "invalid_param" in code:
                return BlenderOutput(
                    status=BlenderExecutionStatus.SCRIPT_ERROR,
                    stdout="",
                    stderr="TypeError: invalid parameter",
                    return_code=1,
                    execution_time=0.5,
                    output_files=[]
                )
            else:
                return BlenderOutput(
                    status=BlenderExecutionStatus.SUCCESS,
                    stdout="Success",
                    stderr="",
                    return_code=0,
                    execution_time=1.0,
                    output_files=["/tmp/output.blend"]
                )
    
    # Run inner loop simulation
    mock_code_agent = MockCodeGenerationAgent()
    mock_blender_executor = MockBlenderExecutor()
    original_spec = {"objects": [{"geometry": {"type": "Cube"}}]}
    
    print("Starting inner loop fix cycle simulation...")
    result = agent.create_inner_loop_fix_cycle(
        mock_code_agent,
        mock_blender_executor,
        original_spec,
        max_attempts=3
    )
    
    print(f"Success: {result['success']}")
    print(f"Attempts: {result['attempts']}")
    print(f"Errors encountered: {len(result['error_history'])}")
    print(f"Fixes generated: {len(result['fix_history'])}")
    
    if result['success']:
        print("✓ Inner loop successfully resolved the errors!")
    else:
        print("✗ Inner loop failed to resolve all errors within max attempts")
    
    print()


def main():
    """Main demo function."""
    print("ValidatorDebuggerAgent Demo")
    print("=" * 60)
    print("This demo showcases the capabilities of the ValidatorDebuggerAgent")
    print("for analyzing Blender execution errors and generating fixes.")
    print()
    
    try:
        # Run demonstrations
        demo_error_classification()
        demo_validation_and_debugging()
        demo_inner_loop_simulation()
        
        print("=" * 60)
        print("Demo completed successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
