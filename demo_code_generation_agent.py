#!/usr/bin/env python3
"""
Demo script for Code Generation Agent

This script demonstrates the functionality of the CodeGenerationAgent,
showing how it converts JSON specifications to Blender Python code.

Author: Augment Agent
Date: 2025-07-18
"""

import json
import os
from pathlib import Path

from agents.code_generation_agent import CodeGenerationAgent, CodeGenerationConfig
from agents.knowledge_agent import KnowledgeAgent


def main():
    """Main demo function."""
    print("=== Code Generation Agent Demo ===\n")
    
    # Initialize the agent
    config = CodeGenerationConfig(
        include_comments=True,
        include_error_handling=True,
        enable_static_analysis=True
    )
    
    # Try to initialize with knowledge agent if available
    knowledge_agent = None
    try:
        knowledge_agent = KnowledgeAgent()
        if knowledge_agent.load_knowledge_base():
            print("✓ Knowledge Agent loaded successfully")
        else:
            print("⚠ Knowledge Agent failed to load, continuing without it")
            knowledge_agent = None
    except Exception as e:
        print(f"⚠ Could not initialize Knowledge Agent: {e}")
        knowledge_agent = None
    
    agent = CodeGenerationAgent(config=config, knowledge_agent=knowledge_agent)
    print("✓ Code Generation Agent initialized\n")
    
    # Demo 1: Simple cube
    print("Demo 1: Simple Cube")
    print("-" * 20)
    
    simple_spec = {
        "schema_version": "v1.0.0",
        "model_info": {
            "name": "Simple Red Cube",
            "description": "A simple red cube for demonstration",
            "created_at": "2025-07-18T10:00:00Z",
            "tags": ["demo", "cube", "simple"]
        },
        "scene_settings": {
            "units": "meters"
        },
        "objects": [
            {
                "id": "cube_1",
                "name": "Red Cube",
                "geometry": {
                    "type": "cube",
                    "size": 2.0
                },
                "transform": {
                    "position": {"x": 0.0, "y": 0.0, "z": 0.0},
                    "rotation": {"x": 0.0, "y": 0.0, "z": 0.0},
                    "scale": {"x": 1.0, "y": 1.0, "z": 1.0}
                },
                "material": {
                    "type": "basic",
                    "name": "red_material",
                    "color": {"r": 1.0, "g": 0.0, "b": 0.0, "a": 1.0}
                }
            }
        ]
    }
    
    result1 = agent.generate_blender_code(simple_spec)
    print_result_summary(result1)
    
    # Save the generated code
    output_dir = Path("demo_output")
    output_dir.mkdir(exist_ok=True)
    
    code_file1 = output_dir / "simple_cube.py"
    with open(code_file1, 'w') as f:
        f.write(result1.generated_code)
    print(f"✓ Code saved to: {code_file1}\n")
    
    # Demo 2: Multiple objects with different geometries
    print("Demo 2: Multiple Objects Scene")
    print("-" * 30)
    
    multi_spec = {
        "schema_version": "v1.0.0",
        "model_info": {
            "name": "Multi-Object Scene",
            "description": "A scene with multiple geometric objects",
            "created_at": "2025-07-18T10:00:00Z",
            "tags": ["demo", "multi-object", "scene"]
        },
        "scene_settings": {
            "units": "meters"
        },
        "objects": [
            {
                "id": "cube_1",
                "name": "Blue Cube",
                "geometry": {
                    "type": "cube",
                    "size": 1.5
                },
                "transform": {
                    "position": {"x": -3.0, "y": 0.0, "z": 0.0}
                },
                "material": {
                    "type": "basic",
                    "name": "blue_material",
                    "color": {"r": 0.0, "g": 0.0, "b": 1.0, "a": 1.0}
                }
            },
            {
                "id": "sphere_1",
                "name": "Green Sphere",
                "geometry": {
                    "type": "sphere",
                    "radius": 1.0,
                    "subdivisions": 4
                },
                "transform": {
                    "position": {"x": 0.0, "y": 0.0, "z": 0.0}
                },
                "material": {
                    "type": "basic",
                    "name": "green_material",
                    "color": {"r": 0.0, "g": 1.0, "b": 0.0, "a": 1.0}
                }
            },
            {
                "id": "cylinder_1",
                "name": "Yellow Cylinder",
                "geometry": {
                    "type": "cylinder",
                    "radius": 0.8,
                    "height": 2.5,
                    "vertices": 16
                },
                "transform": {
                    "position": {"x": 3.0, "y": 0.0, "z": 0.0},
                    "rotation": {"x": 0.0, "y": 0.0, "z": 1.57}  # 90 degrees
                },
                "material": {
                    "type": "basic",
                    "name": "yellow_material",
                    "color": {"r": 1.0, "g": 1.0, "b": 0.0, "a": 1.0}
                }
            }
        ]
    }
    
    result2 = agent.generate_blender_code(multi_spec)
    print_result_summary(result2)
    
    code_file2 = output_dir / "multi_object_scene.py"
    with open(code_file2, 'w') as f:
        f.write(result2.generated_code)
    print(f"✓ Code saved to: {code_file2}\n")
    
    # Demo 3: Static code analysis
    print("Demo 3: Static Code Analysis")
    print("-" * 28)
    
    # Test with some sample code
    test_codes = [
        ("Valid Code", "import bpy\nbpy.ops.mesh.primitive_cube_add(size=2.0)"),
        ("Invalid Syntax", "import bpy\nbpy.ops.mesh.primitive_cube_add(size=2.0"),  # Missing closing parenthesis
        ("Complex Code", """
import bpy
import bmesh

def create_complex_scene():
    for i in range(5):
        if i % 2 == 0:
            bpy.ops.mesh.primitive_cube_add(location=(i*2, 0, 0))
        else:
            try:
                bpy.ops.mesh.primitive_sphere_add(location=(i*2, 0, 0))
            except Exception as e:
                print(f"Error creating sphere: {e}")

create_complex_scene()
""")
    ]
    
    for name, code in test_codes:
        print(f"\nAnalyzing: {name}")
        analysis = agent.validate_code_only(code)
        print(f"  Syntax Valid: {analysis.syntax_valid}")
        print(f"  AST Parseable: {analysis.ast_parseable}")
        print(f"  Quality Score: {analysis.quality_score:.2f}")
        print(f"  Quality Level: {analysis.quality_level.value}")
        print(f"  Line Count: {analysis.line_count}")
        print(f"  Complexity: {analysis.complexity_score}")
        if analysis.issues:
            print(f"  Issues: {', '.join(analysis.issues)}")
        if analysis.suggestions:
            print(f"  Suggestions: {', '.join(analysis.suggestions)}")
    
    # Demo 4: Show supported geometry types
    print(f"\nDemo 4: Supported Geometry Types")
    print("-" * 32)
    supported_types = agent.get_supported_geometry_types()
    print(f"Supported types: {', '.join(supported_types)}")
    
    print(f"\n=== Demo Complete ===")
    print(f"Generated code files saved in: {output_dir.absolute()}")


def print_result_summary(result):
    """Print a summary of the code generation result."""
    print(f"✓ Code generated successfully")
    print(f"  Generation time: {result.generation_time:.3f}s")
    print(f"  Confidence score: {result.confidence_score:.2f}")
    print(f"  Code quality: {result.analysis_result.quality_level.value}")
    print(f"  Lines of code: {result.analysis_result.line_count}")
    print(f"  Syntax valid: {result.analysis_result.syntax_valid}")
    print(f"  Objects processed: {result.metadata.get('objects_count', 0)}")
    if result.knowledge_context_used:
        print(f"  Knowledge context items: {len(result.knowledge_context_used)}")


if __name__ == "__main__":
    main()
