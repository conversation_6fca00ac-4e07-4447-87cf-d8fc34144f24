"""
Code Generation Agent for Blender 3D Model Generation AI Agent System

This module implements a code generation agent that converts JSON specifications
conforming to base_model_spec.json into executable Blender Python (bpy) code.
It integrates with the Knowledge Agent for API assistance and includes static
code analysis using Python's AST module.

Author: Augment Agent
Date: 2025-07-18
"""

import os
import ast
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

import openai
from openai import OpenAI

# Import knowledge agent for API assistance
from agents.knowledge_agent import KnowledgeAgent, RetrievalResult, KnowledgeSource

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CodeGenerationError(Exception):
    """Exception raised for code generation errors."""
    pass


class CodeQuality(Enum):
    """Code quality levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    INVALID = "invalid"


@dataclass
class CodeAnalysisResult:
    """Result of static code analysis."""
    syntax_valid: bool
    ast_parseable: bool
    quality_score: float
    quality_level: CodeQuality
    issues: List[str]
    suggestions: List[str]
    complexity_score: int
    line_count: int


@dataclass
class CodeGenerationConfig:
    """Configuration for code generation."""
    include_comments: bool = True
    include_error_handling: bool = True
    use_knowledge_agent: bool = True
    openai_model: str = "gpt-4"
    max_retries: int = 3
    code_style: str = "pep8"
    target_blender_version: str = "4.0"
    enable_static_analysis: bool = True


@dataclass
class CodeGenerationResult:
    """Result of code generation."""
    generated_code: str
    specification_used: Dict[str, Any]
    analysis_result: CodeAnalysisResult
    knowledge_context_used: List[str]
    generation_time: float
    confidence_score: float
    metadata: Optional[Dict[str, Any]] = None


class CodeGenerationAgent:
    """
    Code Generation Agent for converting 3D model specifications to Blender Python code.
    
    This agent takes JSON specifications conforming to base_model_spec.json and generates
    executable Blender Python (bpy) code with static analysis validation.
    """
    
    def __init__(self, 
                 config: Optional[CodeGenerationConfig] = None,
                 knowledge_agent: Optional[KnowledgeAgent] = None,
                 openai_api_key: Optional[str] = None):
        """
        Initialize the Code Generation Agent.
        
        Args:
            config: Configuration for the agent
            knowledge_agent: Knowledge agent for API assistance
            openai_api_key: OpenAI API key for LLM calls
        """
        self.config = config or CodeGenerationConfig()
        self.knowledge_agent = knowledge_agent
        
        # Initialize OpenAI client
        api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
        if api_key:
            self.openai_client = OpenAI(api_key=api_key)
        else:
            logger.warning("No OpenAI API key provided. LLM-based generation will not be available.")
            self.openai_client = None
        
        # Load code templates
        self.code_templates = self._load_code_templates()
        
        logger.info("CodeGenerationAgent initialized successfully")
    
    def _load_code_templates(self) -> Dict[str, str]:
        """Load Blender Python code templates for different geometry types."""
        templates = {
            "header": '''import bpy
import bmesh
from mathutils import Vector

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Clear existing materials
for material in bpy.data.materials:
    bpy.data.materials.remove(material)

''',
            "cube": '''
# Create cube: {name}
bpy.ops.mesh.primitive_cube_add(
    size={size},
    location=({pos_x}, {pos_y}, {pos_z})
)
cube_obj = bpy.context.active_object
cube_obj.name = "{name}"
cube_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
cube_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "sphere": '''
# Create sphere: {name}
bpy.ops.mesh.primitive_uv_sphere_add(
    radius={radius},
    location=({pos_x}, {pos_y}, {pos_z}),
    subdivisions={subdivisions}
)
sphere_obj = bpy.context.active_object
sphere_obj.name = "{name}"
sphere_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
sphere_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "cylinder": '''
# Create cylinder: {name}
bpy.ops.mesh.primitive_cylinder_add(
    radius={radius},
    depth={height},
    location=({pos_x}, {pos_y}, {pos_z}),
    vertices={vertices}
)
cylinder_obj = bpy.context.active_object
cylinder_obj.name = "{name}"
cylinder_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
cylinder_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "plane": '''
# Create plane: {name}
bpy.ops.mesh.primitive_plane_add(
    size={size},
    location=({pos_x}, {pos_y}, {pos_z})
)
plane_obj = bpy.context.active_object
plane_obj.name = "{name}"
plane_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
plane_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "cone": '''
# Create cone: {name}
bpy.ops.mesh.primitive_cone_add(
    radius1={radius},
    depth={height},
    location=({pos_x}, {pos_y}, {pos_z}),
    vertices={vertices}
)
cone_obj = bpy.context.active_object
cone_obj.name = "{name}"
cone_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
cone_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "material": '''
# Create material: {material_name}
material = bpy.data.materials.new(name="{material_name}")
material.use_nodes = True
bsdf = material.node_tree.nodes["Principled BSDF"]
bsdf.inputs[0].default_value = ({r}, {g}, {b}, {a})  # Base Color
{material_obj}.data.materials.append(material)
''',
            "footer": '''
# Update scene
bpy.context.view_layer.update()

# Optional: Save the file
# bpy.ops.wm.save_as_mainfile(filepath="/path/to/output.blend")
'''
        }
        return templates
    
    def generate_blender_code(self,
                             specification: Dict[str, Any],
                             output_path: Optional[str] = None) -> CodeGenerationResult:
        """
        Generate Blender Python code from a JSON specification.
        
        Args:
            specification: JSON specification conforming to base_model_spec.json
            output_path: Optional path to save the generated code
            
        Returns:
            CodeGenerationResult containing the generated code and analysis
        """
        start_time = datetime.now()
        
        try:
            # Validate input specification
            self._validate_specification(specification)
            
            # Get knowledge context if available
            knowledge_context = []
            if self.config.use_knowledge_agent and self.knowledge_agent:
                knowledge_context = self._get_knowledge_context(specification)
            
            # Generate code using template-based approach
            generated_code = self._generate_code_from_template(specification, knowledge_context)
            
            # Perform static code analysis
            analysis_result = self._analyze_code(generated_code)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(analysis_result, knowledge_context)
            
            # Save code if output path provided
            if output_path:
                self._save_code(generated_code, output_path)
            
            generation_time = (datetime.now() - start_time).total_seconds()
            
            return CodeGenerationResult(
                generated_code=generated_code,
                specification_used=specification,
                analysis_result=analysis_result,
                knowledge_context_used=[ctx.chunk.content[:100] + "..." for ctx in knowledge_context],
                generation_time=generation_time,
                confidence_score=confidence_score,
                metadata={
                    'objects_count': len(specification.get('objects', [])),
                    'generation_method': 'template_based',
                    'blender_version': self.config.target_blender_version
                }
            )
            
        except Exception as e:
            logger.error(f"Code generation failed: {e}")
            raise CodeGenerationError(f"Failed to generate code: {e}")
    
    def _validate_specification(self, specification: Dict[str, Any]):
        """Validate the input specification."""
        required_fields = ['schema_version', 'model_info', 'objects']
        for field in required_fields:
            if field not in specification:
                raise CodeGenerationError(f"Missing required field: {field}")
        
        if not isinstance(specification['objects'], list):
            raise CodeGenerationError("Objects field must be a list")
        
        if len(specification['objects']) == 0:
            raise CodeGenerationError("Objects list cannot be empty")
        
        # Validate each object
        for i, obj in enumerate(specification['objects']):
            if 'geometry' not in obj:
                raise CodeGenerationError(f"Object {i} missing geometry field")

            geometry_type = obj['geometry'].get('type')
            if geometry_type not in ['cube', 'sphere', 'cylinder', 'plane', 'cone']:
                raise CodeGenerationError(f"Unsupported geometry type: {geometry_type}")

    def _get_knowledge_context(self, specification: Dict[str, Any]) -> List[RetrievalResult]:
        """Get relevant knowledge context for code generation."""
        if not self.knowledge_agent:
            return []

        try:
            # Build query from geometry types
            geometry_types = []
            for obj in specification.get('objects', []):
                geometry_type = obj.get('geometry', {}).get('type')
                if geometry_type:
                    geometry_types.append(geometry_type)

            query = f"blender python bpy create {' '.join(set(geometry_types))} mesh primitive"

            # Query knowledge base
            results = self.knowledge_agent.query_knowledge(query, top_k=5)
            logger.info(f"Retrieved {len(results)} knowledge context items")
            return results

        except Exception as e:
            logger.warning(f"Failed to get knowledge context: {e}")
            return []

    def _generate_code_from_template(self,
                                   specification: Dict[str, Any],
                                   knowledge_context: List[RetrievalResult]) -> str:
        """Generate Blender Python code using templates."""
        code_parts = []

        # Add header
        code_parts.append(self.code_templates["header"])

        # Add comment with model info
        model_info = specification.get('model_info', {})
        if self.config.include_comments:
            code_parts.append(f'# Generated model: {model_info.get("name", "Unnamed")}\n')
            code_parts.append(f'# Description: {model_info.get("description", "No description")}\n')
            code_parts.append(f'# Generated at: {datetime.now().isoformat()}\n\n')

        # Process each object
        for obj in specification.get('objects', []):
            object_code = self._generate_object_code(obj)
            code_parts.append(object_code)

        # Add footer
        code_parts.append(self.code_templates["footer"])

        return ''.join(code_parts)

    def _generate_object_code(self, obj: Dict[str, Any]) -> str:
        """Generate code for a single object."""
        geometry = obj.get('geometry', {})
        geometry_type = geometry.get('type')

        # Get object properties with defaults
        name = obj.get('name', f'Object_{uuid.uuid4().hex[:8]}')
        transform = obj.get('transform', {})
        material = obj.get('material', {})

        # Extract transform properties with defaults
        position = transform.get('position', {'x': 0.0, 'y': 0.0, 'z': 0.0})
        rotation = transform.get('rotation', {'x': 0.0, 'y': 0.0, 'z': 0.0})
        scale = transform.get('scale', {'x': 1.0, 'y': 1.0, 'z': 1.0})

        # Prepare template parameters
        params = {
            'name': name,
            'pos_x': position.get('x', 0.0),
            'pos_y': position.get('y', 0.0),
            'pos_z': position.get('z', 0.0),
            'rot_x': rotation.get('x', 0.0),
            'rot_y': rotation.get('y', 0.0),
            'rot_z': rotation.get('z', 0.0),
            'scale_x': scale.get('x', 1.0),
            'scale_y': scale.get('y', 1.0),
            'scale_z': scale.get('z', 1.0)
        }

        # Add geometry-specific parameters
        if geometry_type == 'cube':
            params['size'] = geometry.get('size', 2.0)
        elif geometry_type == 'sphere':
            params['radius'] = geometry.get('radius', 1.0)
            params['subdivisions'] = geometry.get('subdivisions', 4)
        elif geometry_type == 'cylinder':
            params['radius'] = geometry.get('radius', 1.0)
            params['height'] = geometry.get('height', 2.0)
            params['vertices'] = geometry.get('vertices', 32)
        elif geometry_type == 'plane':
            params['size'] = geometry.get('size', 2.0)
        elif geometry_type == 'cone':
            params['radius'] = geometry.get('radius', 1.0)
            params['height'] = geometry.get('height', 2.0)
            params['vertices'] = geometry.get('vertices', 32)

        # Generate geometry code
        geometry_code = self.code_templates[geometry_type].format(**params)

        # Generate material code if material is specified
        material_code = ""
        if material and material.get('type'):
            material_params = {
                'material_name': material.get('name', f'{name}_material'),
                'material_obj': f'{geometry_type}_obj',
                'r': material.get('color', {}).get('r', 0.8),
                'g': material.get('color', {}).get('g', 0.8),
                'b': material.get('color', {}).get('b', 0.8),
                'a': material.get('color', {}).get('a', 1.0)
            }
            material_code = self.code_templates["material"].format(**material_params)

        return geometry_code + material_code + "\n"

    def _analyze_code(self, code: str) -> CodeAnalysisResult:
        """Perform static code analysis using AST."""
        issues = []
        suggestions = []
        syntax_valid = False
        ast_parseable = False
        complexity_score = 0
        line_count = len(code.split('\n'))

        try:
            # Parse code with AST
            tree = ast.parse(code)
            ast_parseable = True
            syntax_valid = True

            # Calculate complexity score (simple metric based on node count)
            complexity_score = self._calculate_complexity(tree)

            # Check for common issues
            issues.extend(self._check_code_issues(tree, code))

            # Generate suggestions
            suggestions.extend(self._generate_suggestions(tree, code))

        except SyntaxError as e:
            issues.append(f"Syntax error: {e}")
            syntax_valid = False
            ast_parseable = False
        except Exception as e:
            issues.append(f"Analysis error: {e}")

        # Calculate quality score
        quality_score = self._calculate_quality_score(syntax_valid, ast_parseable, issues, complexity_score)
        quality_level = self._determine_quality_level(quality_score)

        return CodeAnalysisResult(
            syntax_valid=syntax_valid,
            ast_parseable=ast_parseable,
            quality_score=quality_score,
            quality_level=quality_level,
            issues=issues,
            suggestions=suggestions,
            complexity_score=complexity_score,
            line_count=line_count
        )

    def _calculate_complexity(self, tree: ast.AST) -> int:
        """Calculate code complexity based on AST nodes."""
        complexity = 0
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.For, ast.While, ast.Try)):
                complexity += 1
            elif isinstance(node, ast.FunctionDef):
                complexity += 2
            elif isinstance(node, ast.ClassDef):
                complexity += 3
        return complexity

    def _check_code_issues(self, tree: ast.AST, code: str) -> List[str]:
        """Check for common code issues."""
        issues = []

        # Check for hardcoded values that should be parameterized
        lines = code.split('\n')
        for i, line in enumerate(lines):
            if 'filepath=' in line and '/path/to/' in line:
                issues.append(f"Line {i+1}: Hardcoded file path should be parameterized")

        # Check for missing error handling around bpy operations
        has_error_handling = any(isinstance(node, ast.Try) for node in ast.walk(tree))
        if not has_error_handling and self.config.include_error_handling:
            issues.append("Consider adding error handling for Blender operations")

        return issues

    def _generate_suggestions(self, tree: ast.AST, code: str) -> List[str]:
        """Generate code improvement suggestions."""
        suggestions = []

        # Count function calls to suggest optimization
        bpy_calls = 0
        for node in ast.walk(tree):
            if isinstance(node, ast.Call) and isinstance(node.func, ast.Attribute):
                if isinstance(node.func.value, ast.Name) and node.func.value.id == 'bpy':
                    bpy_calls += 1

        if bpy_calls > 10:
            suggestions.append("Consider batching Blender operations for better performance")

        # Check for documentation
        has_docstring = False
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and ast.get_docstring(node):
                has_docstring = True
                break

        if not has_docstring and self.config.include_comments:
            suggestions.append("Consider adding function documentation")

        return suggestions

    def _calculate_quality_score(self, syntax_valid: bool, ast_parseable: bool,
                                issues: List[str], complexity_score: int) -> float:
        """Calculate overall code quality score (0.0 to 1.0)."""
        if not syntax_valid:
            return 0.0

        if not ast_parseable:
            return 0.1

        # Base score for valid syntax
        score = 0.7

        # Deduct points for issues
        score -= len(issues) * 0.05

        # Adjust for complexity (moderate complexity is good)
        if complexity_score < 5:
            score += 0.1  # Simple is good
        elif complexity_score > 20:
            score -= 0.1  # Too complex

        return max(0.0, min(1.0, score))

    def _determine_quality_level(self, quality_score: float) -> CodeQuality:
        """Determine quality level from score."""
        if quality_score >= 0.9:
            return CodeQuality.EXCELLENT
        elif quality_score >= 0.8:
            return CodeQuality.GOOD
        elif quality_score >= 0.6:
            return CodeQuality.ACCEPTABLE
        elif quality_score >= 0.3:
            return CodeQuality.POOR
        else:
            return CodeQuality.INVALID

    def _calculate_confidence_score(self, analysis_result: CodeAnalysisResult,
                                  knowledge_context: List[RetrievalResult]) -> float:
        """Calculate confidence score for the generated code."""
        confidence = 0.5  # Base confidence

        # Boost confidence for valid syntax and AST
        if analysis_result.syntax_valid:
            confidence += 0.2
        if analysis_result.ast_parseable:
            confidence += 0.1

        # Boost confidence based on quality score
        confidence += analysis_result.quality_score * 0.2

        # Boost confidence if we have knowledge context
        if knowledge_context:
            confidence += min(len(knowledge_context) * 0.02, 0.1)

        # Reduce confidence for issues
        confidence -= len(analysis_result.issues) * 0.05

        return max(0.0, min(1.0, confidence))

    def _save_code(self, code: str, output_path: str):
        """Save generated code to file."""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(code)

            logger.info(f"Code saved to: {output_path}")

        except Exception as e:
            logger.error(f"Failed to save code: {e}")
            raise CodeGenerationError(f"Failed to save code: {e}")

    def validate_code_only(self, code: str) -> CodeAnalysisResult:
        """
        Validate code without generating it.

        Args:
            code: Python code to validate

        Returns:
            CodeAnalysisResult with validation results
        """
        return self._analyze_code(code)

    def get_supported_geometry_types(self) -> List[str]:
        """Get list of supported geometry types."""
        return ['cube', 'sphere', 'cylinder', 'plane', 'cone']

    def get_code_template(self, geometry_type: str) -> str:
        """Get code template for a specific geometry type."""
        if geometry_type not in self.code_templates:
            raise CodeGenerationError(f"Unsupported geometry type: {geometry_type}")
        return self.code_templates[geometry_type]

    def generate_code_with_llm(self,
                              specification: Dict[str, Any],
                              knowledge_context: List[RetrievalResult]) -> str:
        """
        Generate code using LLM (fallback method).

        Args:
            specification: JSON specification
            knowledge_context: Knowledge context from KnowledgeAgent

        Returns:
            Generated Python code
        """
        if not self.openai_client:
            raise CodeGenerationError("OpenAI client not available for LLM generation")

        # Prepare context for LLM
        context_text = self._prepare_llm_context(specification, knowledge_context)

        # Create prompt for code generation
        prompt = self._create_code_generation_prompt(context_text)

        try:
            response = self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert Blender Python developer."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )

            generated_code = response.choices[0].message.content.strip()

            # Extract code from markdown if present
            if "```python" in generated_code:
                start = generated_code.find("```python") + 9
                end = generated_code.find("```", start)
                if end != -1:
                    generated_code = generated_code[start:end].strip()

            return generated_code

        except Exception as e:
            logger.error(f"LLM code generation failed: {e}")
            raise CodeGenerationError(f"LLM generation failed: {e}")

    def _prepare_llm_context(self, specification: Dict[str, Any],
                           knowledge_context: List[RetrievalResult]) -> str:
        """Prepare context for LLM code generation."""
        context_parts = []

        # Add specification info
        context_parts.append("SPECIFICATION:")
        context_parts.append(json.dumps(specification, indent=2))

        # Add knowledge context
        if knowledge_context:
            context_parts.append("\nRELEVANT BLENDER API KNOWLEDGE:")
            for i, result in enumerate(knowledge_context[:3]):
                context_parts.append(f"{i+1}. {result.chunk.content[:300]}...")

        return "\n".join(context_parts)

    def _create_code_generation_prompt(self, context_text: str) -> str:
        """Create prompt for LLM code generation."""
        return f"""Generate Blender Python (bpy) code based on the following specification:

{context_text}

REQUIREMENTS:
1. Generate clean, executable Blender Python code
2. Use proper bpy API calls for creating geometry
3. Include proper object naming and transformations
4. Add materials if specified in the specification
5. Include error handling where appropriate
6. Follow PEP 8 style guidelines
7. Add helpful comments

Generate the complete Python script:"""
