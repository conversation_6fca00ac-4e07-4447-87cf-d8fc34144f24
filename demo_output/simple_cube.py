import bpy
import bmesh
from mathutils import Vector

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Clear existing materials
for material in bpy.data.materials:
    bpy.data.materials.remove(material)

# Generated model: Simple Red Cube
# Description: A simple red cube for demonstration
# Generated at: 2025-07-18T17:22:16.944700


# Create cube: Red Cube
bpy.ops.mesh.primitive_cube_add(
    size=2.0,
    location=(0.0, 0.0, 0.0)
)
cube_obj = bpy.context.active_object
cube_obj.name = "Red Cube"
cube_obj.rotation_euler = (0.0, 0.0, 0.0)
cube_obj.scale = (1.0, 1.0, 1.0)

# Create material: red_material
material = bpy.data.materials.new(name="red_material")
material.use_nodes = True
bsdf = material.node_tree.nodes["Principled BSDF"]
bsdf.inputs[0].default_value = (1.0, 0.0, 0.0, 1.0)  # Base Color
cube_obj.data.materials.append(material)


# Update scene
bpy.context.view_layer.update()

# Optional: Save the file
# bpy.ops.wm.save_as_mainfile(filepath="/path/to/output.blend")
