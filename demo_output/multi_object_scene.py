import bpy
import bmesh
from mathutils import Vector

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Clear existing materials
for material in bpy.data.materials:
    bpy.data.materials.remove(material)

# Generated model: Multi-Object Scene
# Description: A scene with multiple geometric objects
# Generated at: 2025-07-18T17:22:17.124585


# Create cube: Blue Cube
bpy.ops.mesh.primitive_cube_add(
    size=1.5,
    location=(-3.0, 0.0, 0.0)
)
cube_obj = bpy.context.active_object
cube_obj.name = "Blue Cube"
cube_obj.rotation_euler = (0.0, 0.0, 0.0)
cube_obj.scale = (1.0, 1.0, 1.0)

# Create material: blue_material
material = bpy.data.materials.new(name="blue_material")
material.use_nodes = True
bsdf = material.node_tree.nodes["Principled BSDF"]
bsdf.inputs[0].default_value = (0.0, 0.0, 1.0, 1.0)  # Base Color
cube_obj.data.materials.append(material)


# Create sphere: Green Sphere
bpy.ops.mesh.primitive_uv_sphere_add(
    radius=1.0,
    location=(0.0, 0.0, 0.0),
    subdivisions=4
)
sphere_obj = bpy.context.active_object
sphere_obj.name = "Green Sphere"
sphere_obj.rotation_euler = (0.0, 0.0, 0.0)
sphere_obj.scale = (1.0, 1.0, 1.0)

# Create material: green_material
material = bpy.data.materials.new(name="green_material")
material.use_nodes = True
bsdf = material.node_tree.nodes["Principled BSDF"]
bsdf.inputs[0].default_value = (0.0, 1.0, 0.0, 1.0)  # Base Color
sphere_obj.data.materials.append(material)


# Create cylinder: Yellow Cylinder
bpy.ops.mesh.primitive_cylinder_add(
    radius=0.8,
    depth=2.5,
    location=(3.0, 0.0, 0.0),
    vertices=16
)
cylinder_obj = bpy.context.active_object
cylinder_obj.name = "Yellow Cylinder"
cylinder_obj.rotation_euler = (0.0, 0.0, 1.57)
cylinder_obj.scale = (1.0, 1.0, 1.0)

# Create material: yellow_material
material = bpy.data.materials.new(name="yellow_material")
material.use_nodes = True
bsdf = material.node_tree.nodes["Principled BSDF"]
bsdf.inputs[0].default_value = (1.0, 1.0, 0.0, 1.0)  # Base Color
cylinder_obj.data.materials.append(material)


# Update scene
bpy.context.view_layer.update()

# Optional: Save the file
# bpy.ops.wm.save_as_mainfile(filepath="/path/to/output.blend")
